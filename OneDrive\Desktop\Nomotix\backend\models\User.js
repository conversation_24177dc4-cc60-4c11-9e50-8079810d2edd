import mongoose from "mongoose";

const carSchema = new mongoose.Schema({
  _id: { type: mongoose.Schema.Types.ObjectId, default: () => new mongoose.Types.ObjectId() },
  customName: String, // Optional custom name for the car
  make: String,
  model: String,
  year: Number,
  licensePlate: String,
  fuelType: { type: String, enum: ['Petrol', 'Diesel', 'CNG', 'Electric', 'Hybrid'], default: 'Petrol' },
}, { _id: false });

const addressSchema = new mongoose.Schema({
  street: String,
  city: String,
  state: String,
  zipCode: String,
  country: String,
}, { _id: false });

const UserSchema = new mongoose.Schema({
  _id: { type: mongoose.Schema.Types.ObjectId, default: () => new mongoose.Types.ObjectId() },
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  phone: { type: String, required: true, unique: true },
  password: { type: String }, // Optional if using Google login
  authProvider: { type: String, enum: ['local', 'google'], default: 'local' },
  googleId: { type: String }, // for Google OAuth
  address: addressSchema,
  cars: [carSchema],
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model("User", UserSchema);