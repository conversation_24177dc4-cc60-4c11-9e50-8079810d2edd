import User from "../models/User.js";

export const getUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select("-password");
    if (!user) return res.status(404).json({ message: "User not found" });
    res.status(200).json(user);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
export const updateUserProfile = async (req, res) => {
  const { name, email, phone, address } = req.body;
  try {
    const user = await User.findById(req.user._id);
    if (!user) return res.status(404).json({ message: "User not found" });

    user.name = name || user.name;
    user.email = email || user.email;
    user.phone = phone || user.phone;
    user.address = address || user.address;

    const updatedUser = await user.save();
    res.status(200).json(updatedUser);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
export const addCar = async (req, res) => {
  let { make, model, year, licensePlate, fuelType } = req.body;
  console.log(make, model, year, licensePlate, fuelType );
  try {
    const user = await User.findById(req.user._id);
    if (!user) return res.status(404).json({ message: "User not found" });
    year = parseInt(year, 10); // Ensure year is a number
    user.cars.push({ make, model, year, licensePlate, fuelType });
    const updatedUser = await user.save();
    res.status(200).json(updatedUser);
  } catch (err) {
    console.log(err);
    res.status(500).json({ message: err.message });
  }
};
export const updateCar = async (req, res) => {
  const { carId, make, model, year, licensePlate, fuelType } = req.body;
  try {
    const user = await User.findById(req.user._id);
    if (!user) return res.status(404).json({ message: "User not found" });

    const car = user.cars.id(carId);
    if (!car) return res.status(404).json({ message: "Car not found" });

    car.make = make || car.make;
    car.model = model || car.model;
    car.year = year || car.year;
    car.licensePlate = licensePlate || car.licensePlate;
    car.fuelType = fuelType || car.fuelType;

    const updatedUser = await user.save();
    res.status(200).json(updatedUser);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
export const deleteCar = async (req, res) => {
  const { carId } = req.body;
  try {
    const user = await User.findById(req.user._id);
    if (!user) return res.status(404).json({ message: "User not found" });

    const car = user.cars.id(carId);
    if (!car) return res.status(404).json({ message: "Car not found" });

    car.remove();
    const updatedUser = await user.save();
    res.status(200).json(updatedUser);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
export const getUserCars = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select("cars");
    if (!user) return res.status(404).json({ message: "User not found" });
    res.status(200).json(user.cars);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};