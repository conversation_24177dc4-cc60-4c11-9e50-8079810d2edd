import React, { useState } from 'react';
import api from '../api';
import { useNavigate } from 'react-router-dom';
import { GoogleLogin } from '@react-oauth/google';
import { jwtDecode } from "jwt-decode";

export default function Login() {
  const [form, setForm] = useState({ email: '', password: '' });
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const res = await api.post('/auth/login', form,{
        withCredentials: true
        });
      alert('Login successful');
        localStorage.setItem("user", JSON.stringify(res.data.user));
         localStorage.setItem("token", res.data.token); // if using JWT

      navigate('/');
    } catch (err) {
      console.error(err.response?.data?.message || err.message);
    }
  };

  const handleGoogleLogin = async (credentialResponse) => {
    const decoded = jwtDecode(credentialResponse.credential);
    try {
      await api.post('/auth/google', {
        name: decoded.name,
        email: decoded.email,
        googleId: decoded.sub,
      });
      navigate('/services');
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded shadow-md w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6 text-center">Login to Your Account</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded"
            placeholder="Email"
            name="email"
            type="email"
            value={form.email}
            onChange={e => setForm({ ...form, email: e.target.value })}
          />
          <input
            className="w-full px-4 py-2 border border-gray-300 rounded"
            placeholder="Password"
            type="password"
            name="password"
            value={form.password}
            onChange={e => setForm({ ...form, password: e.target.value })}
          />
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition"
          >
            Login
          </button>
        </form>
        <div className="mt-4">
          <GoogleLogin
            onSuccess={handleGoogleLogin}
            onError={() => alert('Login Failed')}
          />
        </div>
        <p className="text-center text-sm mt-4">
          New here? <a href="/signup" className="text-blue-600 hover:underline">Sign up</a>
        </p>
      </div>
    </div>
  );

}