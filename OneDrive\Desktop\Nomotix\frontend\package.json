{"name": "og", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@gsap/react": "^2.1.1", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@react-leaflet/core": "^2.1.0", "@react-oauth/google": "^0.12.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.12.5", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.469.0", "react": "^18.3.1", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-google-button": "^0.8.0", "react-icons": "^5.4.0", "react-leaflet": "^4.2.1", "react-router-dom": "^7.6.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.0.1"}}