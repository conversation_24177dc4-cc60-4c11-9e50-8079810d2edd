import mongoose from "mongoose";
const Orderschema = mongoose.Schema({
    Customer:{type:mongoose.Schema.Types.ObjectId,ref:'User',required:true},
    serviceType:{type:String,enum:['breakdown','normal'],required:true},
    services:[{type:mongoose.Schema.Types.Mixed}],
    garage:{type:mongoose.Schema.Types.ObjectId,ref:"Garage",required:false},
    breakdownDetails:{
        location:{
            lat:Number,
            lng:Number,
            address:String,
        },
        Status:{
            type:String,enum:["pending","accepted","completed","in-progress"],default:"pending",
        },
        assignedGarage: { type: mongoose.Schema.Types.ObjectId, ref: "Garage" },
        cancellationReason: String,
        assignmentHistory: [
            {
            garage: { type: mongoose.Schema.Types.ObjectId, ref: "Garage" },
            status: { type: String, enum: ["assigned", "cancelled", "reassigned"] },
            timestamp: Date,
            comment: String,
    }
  ]
    },
    bill:{
        total:Number,
        breakdown:[{
            Service:String,
            Cost:Number,
        }],
    },
    status:{type:String,enum:["pending","accepted","completed","in-progress"],default:"pending"},
    paymentStatus:{type:String,enum:["paid","unpaid"],default:"unpaid"},
    paymentMethod:{type:String,enum:["cash","card","upi"],default:"cash"},
    paymentDetails:{
        transactionId:String,
        paymentGateway:String,
        amount:Number,
    },
    createdAt:{type:Date,default:Date.now},
    scheduledAt:Date,
});

export default mongoose.model('Order', Orderschema);    

